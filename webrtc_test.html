<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC Video Call Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-lg p-8 max-w-4xl w-full">
        <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">WebRTC Video Call Test</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Instructions -->
            <div class="space-y-4">
                <h2 class="text-xl font-semibold text-gray-700">How to Test WebRTC Video Calls:</h2>
                <ol class="list-decimal list-inside space-y-2 text-gray-600">
                    <li>Open the main application in multiple browser tabs or different browsers</li>
                    <li>Create a room in one tab and note the Room ID</li>
                    <li>Join the same room from other tabs using the Room ID</li>
                    <li>Click the video camera button to start a video call</li>
                    <li>Other users will see a notification and can join the call</li>
                    <li>Test audio/video toggle buttons and connection quality</li>
                </ol>
                
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6">
                    <h3 class="font-semibold text-blue-800 mb-2">Features Implemented:</h3>
                    <ul class="list-disc list-inside space-y-1 text-blue-700 text-sm">
                        <li>Real-time video and audio streaming</li>
                        <li>Group meetings with multiple participants</li>
                        <li>WebRTC peer-to-peer connections</li>
                        <li>Automatic reconnection on connection failures</li>
                        <li>Connection quality indicators</li>
                        <li>Responsive participant grid layout</li>
                        <li>Audio/video toggle controls</li>
                        <li>Meeting duration timer</li>
                    </ul>
                </div>
            </div>
            
            <!-- Technical Details -->
            <div class="space-y-4">
                <h2 class="text-xl font-semibold text-gray-700">Technical Implementation:</h2>
                
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="font-semibold text-gray-800 mb-2">Backend (collaboration_server.py):</h3>
                    <ul class="list-disc list-inside space-y-1 text-gray-600 text-sm">
                        <li>WebRTC signaling via WebSocket</li>
                        <li>SDP offer/answer exchange</li>
                        <li>ICE candidate relay</li>
                        <li>Room-based message routing</li>
                    </ul>
                </div>
                
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="font-semibold text-gray-800 mb-2">Frontend (frontend.html):</h3>
                    <ul class="list-disc list-inside space-y-1 text-gray-600 text-sm">
                        <li>RTCPeerConnection management</li>
                        <li>Media stream handling</li>
                        <li>Connection state monitoring</li>
                        <li>Automatic error recovery</li>
                        <li>Responsive UI components</li>
                    </ul>
                </div>
                
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h3 class="font-semibold text-green-800 mb-2">Browser Requirements:</h3>
                    <ul class="list-disc list-inside space-y-1 text-green-700 text-sm">
                        <li>Modern browser with WebRTC support</li>
                        <li>Camera and microphone permissions</li>
                        <li>HTTPS for production (HTTP for localhost)</li>
                        <li>Stable internet connection</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="mt-8 text-center">
            <a href="frontend.html" class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors">
                Open Main Application
            </a>
        </div>
        
        <div class="mt-6 text-center text-sm text-gray-500">
            <p>Make sure the collaboration server is running before testing.</p>
            <p>Use <code class="bg-gray-200 px-2 py-1 rounded">python collaboration_server.py</code> to start the server.</p>
        </div>
    </div>
</body>
</html>
