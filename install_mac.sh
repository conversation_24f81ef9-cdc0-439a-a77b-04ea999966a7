#!/bin/bash

# Collaborative Whiteboard - Mac Installation Script
# This script sets up everything needed to run the whiteboard on macOS

echo "🍎 Collaborative Whiteboard - Mac Setup"
echo "========================================"
echo ""

# Check if we're on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "❌ This script is for macOS only!"
    echo "For other systems, please use the appropriate setup guide."
    exit 1
fi

echo "🔍 Checking system requirements..."

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 not found!"
    echo ""
    echo "Installing Python 3..."
    
    # Check if Homebrew is installed
    if ! command -v brew &> /dev/null; then
        echo "📦 Installing Homebrew first..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    fi
    
    echo "📦 Installing Python 3..."
    brew install python3
    
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install Python 3"
        echo "Please install manually from https://python.org"
        exit 1
    fi
else
    echo "✅ Python 3 found: $(python3 --version)"
fi

# Check if pip3 is available
if ! command -v pip3 &> /dev/null; then
    echo "📦 Installing pip3..."
    curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py
    python3 get-pip.py
    rm get-pip.py
fi

echo "✅ pip3 found: $(pip3 --version)"

# Install required Python packages
echo ""
echo "📦 Installing required Python packages..."
pip3 install flask flask-socketio websockets requests python-dotenv Pillow

if [ $? -ne 0 ]; then
    echo "❌ Failed to install some packages"
    echo "Please run manually: pip3 install flask flask-socketio websockets requests python-dotenv Pillow"
    exit 1
fi

echo "✅ All Python packages installed successfully!"

# Make shell scripts executable
echo ""
echo "🔧 Making scripts executable..."
chmod +x *.sh

if [ $? -eq 0 ]; then
    echo "✅ Scripts are now executable!"
else
    echo "⚠️  Could not make scripts executable. You may need to run: chmod +x *.sh"
fi

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo ""
    echo "📝 Creating .env template file..."
    echo "GEMINI_API_KEY=your_api_key_here" > .env
    echo "✅ .env file created!"
    echo "⚠️  Please edit .env and add your actual Gemini API key"
else
    echo "✅ .env file already exists"
fi

# Check if ngrok is installed (optional)
echo ""
echo "🌍 Checking for ngrok (optional for global access)..."
if command -v ngrok &> /dev/null; then
    echo "✅ ngrok found: $(ngrok version)"
else
    echo "⚠️  ngrok not found (optional)"
    echo ""
    echo "To install ngrok for global access:"
    echo "1. brew install ngrok/ngrok/ngrok"
    echo "2. Or download from https://ngrok.com/download"
fi

echo ""
echo "🎉 Setup Complete!"
echo "========================================"
echo ""
echo "✅ Python 3 installed"
echo "✅ Required packages installed"
echo "✅ Scripts made executable"
echo "✅ .env template created"
echo ""
echo "📋 Next Steps:"
echo "1. Edit .env file and add your Gemini API key"
echo "2. Choose how to run:"
echo ""
echo "   🖥️  Localhost only:     ./start_local.sh"
echo "   🌐 Network access:     ./start_local_network.sh"
echo "   🌍 Global access:      ./start_ngrok.sh"
echo ""
echo "📖 For detailed instructions, see MAC_SETUP_GUIDE.md"
echo ""
read -p "Press Enter to exit..."
