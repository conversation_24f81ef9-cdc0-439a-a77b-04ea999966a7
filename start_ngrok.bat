@echo off
echo Starting Collaborative Whiteboard with ngrok (Global Access)
echo.

echo Checking for required files...
if not exist "ngrok_app.py" (
    echo ERROR: ngrok_app.py not found!
    echo Please make sure you're in the correct directory.
    pause
    exit /b 1
)

if not exist ".env" (
    echo WARNING: .env file not found!
    echo Creating a template .env file...
    echo GEMINI_API_KEY=your_api_key_here > .env
    echo Please edit .env file and add your actual API key.
    pause
)

echo Checking for ngrok...
where ngrok >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: ngrok not found!
    echo Please install ngrok from https://ngrok.com/download
    echo Or place ngrok.exe in this folder.
    pause
    exit /b 1
)

echo.
echo ========================================
echo STARTING GLOBAL ACCESS SETUP...
echo ========================================
echo.

echo Step 1: Starting Flask + WebSocket server...
start "Collaborative Whiteboard Server" cmd /k "python ngrok_app.py"

echo Waiting for server to start...
timeout /t 3 /nobreak >nul

echo.
echo Step 2: Starting ngrok tunnel...
start "ngrok Tunnel" cmd /k "ngrok http 5002"

echo.
echo ========================================
echo GLOBAL SETUP COMPLETE!
echo ========================================
echo.
echo 1. Wait for both windows to fully load
echo 2. In the ngrok window, look for the HTTPS URL
echo 3. Share that URL with anyone worldwide!
echo.
echo Example URL: https://abc123.ngrok-free.app
echo.
echo Features available:
echo - Real-time collaborative drawing
echo - Google Meet style video conferencing
echo - AI assistant for questions
echo - Cross-platform support (any device with browser)
echo.
echo To stop: Close both command windows
echo.
pause
