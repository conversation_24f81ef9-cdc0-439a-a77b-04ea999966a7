# 🎨 Collaborative Whiteboard Setup Guide

## 🖥️ Cross-Platform Support

This whiteboard works on **Windows**, **macOS**, and **Linux**!

### 📋 Prerequisites

1. **Python 3.7+** installed on your device
2. **Internet connection** for package installation
3. **Gemini API Key** (for AI features)

## 🚀 Quick Start by Operating System

### 🪟 **Windows Users:**
1. **Double-click:** `start_local.bat` (localhost only)
2. **Or:** `start_local_network.bat` (network access)
3. **Or:** `start_ngrok.bat` (global access)

### 🍎 **Mac Users:**
1. **Run:** `./install_mac.sh` (one-time setup)
2. **Then:** `./start_local.sh` (localhost only)
3. **Or:** `./start_local_network.sh` (network access)
4. **Or:** `./start_ngrok.sh` (global access)

### 🐧 **Linux Users:**
Same as Mac - use the `.sh` scripts

## 🚀 Quick Setup

### 1. Install Required Packages
```bash
pip install flask flask-socketio websockets requests python-dotenv
```

### 2. Create API Key File
Create a file named `.env` in the project folder:
```
GEMINI_API_KEY=your_actual_api_key_here
```

### 3. Choose Your Setup Method

## 🖥️ Option A: Single Device (Localhost Only)

**Use this if:** You only want to use the whiteboard on one device.

**Run:** Double-click `start_local.bat`

**Access:** http://localhost:5002

---

## 🌐 Option B: Multiple Devices on Same Network

**Use this if:** You want multiple devices (phones, tablets, laptops) on the same WiFi to collaborate.

### On the main device (server):
1. Double-click `start_local_network.bat`
2. Note the IP address shown (e.g., `*************`)

### On other devices:
1. Open web browser
2. Go to: `http://[IP_ADDRESS]:5002`
   - Example: `http://*************:5002`

**Requirements:**
- All devices must be on the same WiFi network
- Firewall may need to allow port 5002

---

## 🌍 Option C: Global Access (Internet)

**Use this if:** You want to access from anywhere in the world or share with remote users.

### Method 1: Using ngrok (Recommended)
1. Install ngrok: https://ngrok.com/download
2. Double-click `start_ngrok.bat`
3. Use the provided ngrok URL (e.g., `https://abc123.ngrok-free.app`)

### Method 2: Manual Setup
1. Run: `python ngrok_app.py --local-network`
2. Set up port forwarding on your router (port 5002)
3. Use your public IP address

---

## 📱 Device-Specific Instructions

### Windows PC/Laptop:
- Use any of the `.bat` files
- Make sure Python is in your PATH

### Mac/Linux:
```bash
# Localhost only
python ngrok_app.py

# Network access
python ngrok_app.py --local-network
```

### Mobile Devices (Phone/Tablet):
- Cannot run the server
- Can only connect as clients to existing servers
- Use the web browser to access the URL

---

## 🔧 Troubleshooting

### "Python not found"
- Install Python from python.org
- Make sure to check "Add to PATH" during installation

### "Module not found"
- Run: `pip install flask flask-socketio websockets requests python-dotenv`

### "Can't connect from other devices"
- Check if all devices are on same WiFi
- Try disabling firewall temporarily
- Make sure you're using the correct IP address

### "AI not working"
- Check your `.env` file has the correct API key
- Make sure the API key is valid and has credits

---

## 🎯 Features

✅ **Real-time Collaboration** - Multiple users can draw simultaneously  
✅ **Google Meet Style Video** - Built-in video conferencing  
✅ **AI Assistant** - Ask questions about your drawings  
✅ **Cross-Platform** - Works on any device with a web browser  
✅ **Synchronized Drawing** - All changes sync in real-time  
✅ **Room System** - Create/join private rooms  

---

## 📞 Support

If you need help:
1. Check the console output for error messages
2. Make sure all prerequisites are installed
3. Try the localhost version first to test basic functionality
