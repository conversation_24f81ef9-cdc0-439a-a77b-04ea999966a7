@echo off
echo Starting Collaborative Whiteboard with Ngrok Pro
echo.

echo Step 1: Starting Flask server...
start "Flask Server" cmd /k "python app.py"

echo.
echo Step 2: Starting WebSocket server...
start "WebSocket Server" cmd /k "python collaboration_server.py"

echo.
echo Step 3: Waiting for servers to start...
timeout /t 5 /nobreak > nul

echo.
echo Step 4: Starting ngrok HTTP tunnel...
start "Ngrok HTTP" cmd /k "ngrok http 5002"

echo.
echo Step 5: Starting ngrok WebSocket tunnel...
start "Ngrok WebSocket" cmd /k "ngrok tcp 8765"

echo.
echo ========================================
echo SETUP COMPLETE!
echo ========================================
echo.
echo 1. Wait for all windows to show "online" status
echo 2. Copy the HTTP ngrok URL (https://abc123.ngrok.io)
echo 3. Copy the TCP ngrok URL (tcp://0.tcp.ngrok.io:12345)
echo 4. Update frontend.html with the TCP URL if needed
echo 5. Open the HTTP URL in your browser
echo.
pause
