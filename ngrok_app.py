import os
import asyncio
import threading
import json
import uuid
import logging
import base64
import io
from typing import Dict, Set
from flask import Flask, send_from_directory, request, jsonify
from flask_cors import CORS
from flask_sock import Sock
import google.generativeai as genai
try:
    from PIL import Image
except ImportError:
    print("PIL (Pillow) not found. Installing...")
    import subprocess
    subprocess.check_call(["pip", "install", "Pillow"])
    from PIL import Image

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Flask App Setup
app = Flask(__name__)
CORS(app)
sock = Sock(app)

# Configuration
PORT = int(os.environ.get('PORT', 5002))
API_KEY = os.environ.get('API_KEY', 'AIzaSyBdH-Gig7TYSJvT8eGpi8dDtGMGtoY1tTE')

# Configure Gemini AI
genai.configure(api_key=API_KEY)
model = genai.GenerativeModel('gemini-1.5-flash-latest')

# Global state for collaboration
users: Dict[str, dict] = {}
rooms: Dict[str, dict] = {}
user_connections: Dict[str, dict] = {}

def generate_room_id():
    return ''.join([chr(65 + (int(uuid.uuid4().hex[i], 16) % 26)) for i in range(8)])

@sock.route('/ws')
def handle_websocket(ws):
    user_id = None
    try:
        logger.info("WebSocket connection opened")
        while True:
            try:
                message = ws.receive()
                data = json.loads(message)
                message_type = data.get('type')

                if message_type == 'register':
                    user_id = str(uuid.uuid4())
                    users[user_id] = {
                        'id': user_id,
                        'name': data.get('name', 'Anonymous'),
                        'room_id': None
                    }
                    user_connections[user_id] = ws

                    ws.send(json.dumps({
                        'type': 'registered',
                        'user_id': user_id,
                        'name': users[user_id]['name']
                    }))
                    logger.info(f"User registered: {users[user_id]['name']} ({user_id})")
                
                elif message_type == 'create_room':
                    if user_id and user_id in users:
                        room_id = generate_room_id()

                        # Get initial canvas state from the request
                        initial_canvas_state = data.get('initial_canvas_state', {
                            'objects': [],
                            'background': '#ffffff'
                        })

                        logger.info(f"Creating room {room_id} with initial canvas state: {len(initial_canvas_state.get('objects', []))} objects")

                        rooms[room_id] = {
                            'id': room_id,
                            'name': data.get('room_name', f'Room {room_id}'),
                            'users': [user_id],
                            'max_users': data.get('max_users', 10),
                            'canvas_state': initial_canvas_state
                        }
                        users[user_id]['room_id'] = room_id

                        # Send room created confirmation
                        ws.send(json.dumps({
                            'type': 'room_created',
                            'success': True,
                            'room_id': room_id,
                            'room_name': rooms[room_id]['name']
                        }))

                        # Also send the canvas state back to the creator
                        ws.send(json.dumps({
                            'type': 'canvas_state',
                            'state': rooms[room_id]['canvas_state'],
                            'room': {
                                'id': room_id,
                                'name': rooms[room_id]['name'],
                                'user_count': len(rooms[room_id]['users'])
                            },
                            'users': [{'id': user_id, 'name': users[user_id]['name']}]
                        }))

                        logger.info(f"Room created: {room_id} by user {user_id} with {len(initial_canvas_state.get('objects', []))} initial objects")
                
                elif message_type == 'join_room':
                    if user_id and user_id in users:
                        room_id = data.get('room_id')
                        if room_id in rooms:
                            if user_id not in rooms[room_id]['users']:
                                rooms[room_id]['users'].append(user_id)
                            users[user_id]['room_id'] = room_id

                            # Send room joined confirmation with canvas state
                            ws.send(json.dumps({
                                'type': 'room_joined',
                                'success': True,
                                'room_id': room_id,
                                'room_name': rooms[room_id]['name'],
                                'users': [{'id': uid, 'name': users[uid]['name']} for uid in rooms[room_id]['users'] if uid in users]
                            }))

                            # Send current canvas state to the new user
                            ws.send(json.dumps({
                                'type': 'canvas_state',
                                'state': rooms[room_id]['canvas_state'],
                                'room': {
                                    'id': room_id,
                                    'name': rooms[room_id]['name'],
                                    'user_count': len(rooms[room_id]['users'])
                                },
                                'users': [{'id': uid, 'name': users[uid]['name']} for uid in rooms[room_id]['users'] if uid in users]
                            }))

                            # Broadcast to other users
                            for other_user_id in rooms[room_id]['users']:
                                if other_user_id != user_id and other_user_id in user_connections:
                                    try:
                                        user_connections[other_user_id].send(json.dumps({
                                            'type': 'user_joined',
                                            'user': {'id': user_id, 'name': users[user_id]['name']}
                                        }))
                                    except:
                                        pass

                            logger.info(f"User {user_id} joined room {room_id}")
                        else:
                            # Room doesn't exist
                            ws.send(json.dumps({
                                'type': 'room_joined',
                                'success': False,
                                'error': 'Room not found'
                            }))
                            logger.warning(f"User {user_id} tried to join non-existent room {room_id}")
                
                elif message_type == 'canvas_event':
                    if user_id and user_id in users and users[user_id]['room_id']:
                        room_id = users[user_id]['room_id']
                        if room_id in rooms:
                            event_data = data.get('event', {})
                            event_type = event_data.get('type')

                            # Update canvas state based on event type
                            room = rooms[room_id]
                            canvas_state = room['canvas_state']

                            logger.info(f"Canvas event: {event_type} from user {user_id} in room {room_id}")

                            # Handle different canvas operations
                            if event_type in ['object_added', 'path_created']:
                                obj_data = event_data.get('object') or event_data.get('path')
                                if obj_data:
                                    canvas_state['objects'].append(obj_data)
                                    logger.info(f"Added object to canvas state. Total objects: {len(canvas_state['objects'])}")

                            elif event_type == 'object_modified':
                                obj_id = event_data.get('object_id')
                                obj_data = event_data.get('object')
                                if obj_id and obj_data:
                                    # Find and update the object
                                    for i, obj in enumerate(canvas_state['objects']):
                                        if obj.get('id') == obj_id:
                                            canvas_state['objects'][i] = obj_data
                                            logger.info(f"Modified object {obj_id} in canvas state")
                                            break

                            elif event_type == 'object_removed':
                                obj_id = event_data.get('object_id')
                                if obj_id:
                                    initial_count = len(canvas_state['objects'])
                                    canvas_state['objects'] = [
                                        obj for obj in canvas_state['objects']
                                        if obj.get('id') != obj_id
                                    ]
                                    final_count = len(canvas_state['objects'])
                                    logger.info(f"Removed object {obj_id}. Objects: {initial_count} -> {final_count}")

                            elif event_type == 'canvas_cleared':
                                canvas_state['objects'] = []
                                if 'background' in event_data:
                                    canvas_state['background'] = event_data['background']
                                logger.info("Canvas cleared and state updated")

                            elif event_type == 'background_changed':
                                canvas_state['background'] = event_data.get('background', '#ffffff')
                                logger.info(f"Background changed to: {canvas_state['background']}")

                            # Broadcast to other users in the room
                            for other_user_id in rooms[room_id]['users']:
                                if other_user_id != user_id and other_user_id in user_connections:
                                    try:
                                        user_connections[other_user_id].send(json.dumps({
                                            'type': 'canvas_event',
                                            'event': event_data,
                                            'user_id': user_id
                                        }))
                                    except:
                                        pass

                elif message_type == 'cursor_move':
                    if user_id and user_id in users and users[user_id]['room_id']:
                        room_id = users[user_id]['room_id']
                        if room_id in rooms:
                            logger.info(f"Cursor move from user {user_id}: x={data.get('x')}, y={data.get('y')}")
                            # Broadcast cursor position to other users in the room
                            for other_user_id in rooms[room_id]['users']:
                                if other_user_id != user_id and other_user_id in user_connections:
                                    try:
                                        user_connections[other_user_id].send(json.dumps({
                                            'type': 'cursor_move',
                                            'user_id': user_id,
                                            'x': data.get('x'),
                                            'y': data.get('y')
                                        }))
                                        logger.info(f"Sent cursor position to user {other_user_id}")
                                    except Exception as e:
                                        logger.error(f"Failed to send cursor to user {other_user_id}: {e}")
                    else:
                        logger.warning(f"Cursor move ignored - user_id: {user_id}, in_users: {user_id in users if user_id else False}, room_id: {users.get(user_id, {}).get('room_id') if user_id else None}")

                elif message_type == 'update_name':
                    if user_id and user_id in users:
                        new_name = data.get('name', 'Anonymous')
                        old_name = users[user_id]['name']
                        users[user_id]['name'] = new_name

                        # Broadcast name update to room members
                        room_id = users[user_id].get('room_id')
                        if room_id and room_id in rooms:
                            for other_user_id in rooms[room_id]['users']:
                                if other_user_id != user_id and other_user_id in user_connections:
                                    try:
                                        user_connections[other_user_id].send(json.dumps({
                                            'type': 'user_name_updated',
                                            'user_id': user_id,
                                            'old_name': old_name,
                                            'new_name': new_name
                                        }))
                                    except Exception as e:
                                        logger.error(f"Failed to send name update to user {other_user_id}: {e}")

                        logger.info(f"User {user_id} updated name from '{old_name}' to '{new_name}'")

                elif message_type == 'leave_room':
                    if user_id and user_id in users:
                        room_id = users[user_id].get('room_id')
                        if room_id and room_id in rooms:
                            # Remove user from room
                            if user_id in rooms[room_id]['users']:
                                rooms[room_id]['users'].remove(user_id)

                            # Broadcast user left to other room members
                            for other_user_id in rooms[room_id]['users']:
                                if other_user_id in user_connections:
                                    try:
                                        user_connections[other_user_id].send(json.dumps({
                                            'type': 'user_left',
                                            'user_id': user_id,
                                            'user_name': users[user_id]['name']
                                        }))
                                    except Exception as e:
                                        logger.error(f"Failed to send user left to user {other_user_id}: {e}")

                            # Clean up empty room
                            if not rooms[room_id]['users']:
                                del rooms[room_id]
                                logger.info(f"Room {room_id} deleted (empty)")

                            # Clear user's room
                            users[user_id]['room_id'] = None

                            # Send confirmation to leaving user
                            ws.send(json.dumps({
                                'type': 'room_left',
                                'success': True
                            }))

                            logger.info(f"User {user_id} left room {room_id}")

                elif message_type == 'video_call_started':
                    if user_id and user_id in users:
                        room_id = users[user_id].get('room_id')
                        if room_id and room_id in rooms:
                            user_name = data.get('user_name', users[user_id]['name'])

                            # Broadcast video call start to other room members
                            for other_user_id in rooms[room_id]['users']:
                                if other_user_id != user_id and other_user_id in user_connections:
                                    try:
                                        user_connections[other_user_id].send(json.dumps({
                                            'type': 'video_call_started',
                                            'user_id': user_id,
                                            'user_name': user_name
                                        }))
                                    except Exception as e:
                                        logger.error(f"Failed to send video call start to user {other_user_id}: {e}")

                            logger.info(f"User {user_id} started video call in room {room_id}")

                elif message_type == 'video_call_ended':
                    if user_id and user_id in users:
                        room_id = users[user_id].get('room_id')
                        if room_id and room_id in rooms:
                            # Broadcast video call end to other room members
                            for other_user_id in rooms[room_id]['users']:
                                if other_user_id != user_id and other_user_id in user_connections:
                                    try:
                                        user_connections[other_user_id].send(json.dumps({
                                            'type': 'video_call_ended',
                                            'user_id': user_id
                                        }))
                                    except Exception as e:
                                        logger.error(f"Failed to send video call end to user {other_user_id}: {e}")

                            logger.info(f"User {user_id} ended video call in room {room_id}")

                elif message_type == 'media_status':
                    if user_id and user_id in users:
                        room_id = users[user_id].get('room_id')
                        if room_id and room_id in rooms:
                            video_enabled = data.get('video_enabled', False)
                            audio_enabled = data.get('audio_enabled', False)

                            # Broadcast media status to other room members
                            for other_user_id in rooms[room_id]['users']:
                                if other_user_id != user_id and other_user_id in user_connections:
                                    try:
                                        user_connections[other_user_id].send(json.dumps({
                                            'type': 'media_status',
                                            'user_id': user_id,
                                            'video_enabled': video_enabled,
                                            'audio_enabled': audio_enabled
                                        }))
                                    except Exception as e:
                                        logger.error(f"Failed to send media status to user {other_user_id}: {e}")

                            logger.info(f"User {user_id} updated media status - video: {video_enabled}, audio: {audio_enabled}")

            except json.JSONDecodeError:
                logger.error("Invalid JSON received")
            except Exception as e:
                logger.error(f"Error handling message: {e}")
                break

    except Exception as e:
        logger.error(f"WebSocket error: {e}")
    finally:
        # Cleanup
        if user_id:
            if user_id in user_connections:
                del user_connections[user_id]
            if user_id in users:
                room_id = users[user_id].get('room_id')
                if room_id and room_id in rooms:
                    if user_id in rooms[room_id]['users']:
                        rooms[room_id]['users'].remove(user_id)
                    if not rooms[room_id]['users']:
                        del rooms[room_id]
                        logger.info(f"Room {room_id} deleted (empty)")
                del users[user_id]
            logger.info(f"User unregistered: {user_id}")

# Flask Routes
@app.route('/')
def serve_index():
    return send_from_directory('.', 'frontend.html')

@app.route('/<path:filename>')
def serve_static(filename):
    return send_from_directory('.', filename)

@app.route('/api/chat', methods=['POST'])
def chat_with_ai():
    try:
        logger.info("🔥 API /api/chat endpoint hit!")
        data = request.get_json()
        logger.info(f"📥 Received data keys: {list(data.keys()) if data else 'None'}")

        message = data.get('message', '')
        image_data = data.get('image_data', None)

        logger.info(f"📝 Message length: {len(message) if message else 0}")
        logger.info(f"🖼️ Image data present: {bool(image_data)}")
        if image_data:
            logger.info(f"🖼️ Image data length: {len(image_data)}")

        if not message and not image_data:
            logger.warning("❌ No message or image provided")
            return jsonify({'error': 'No message or image provided'}), 400

        # Prepare content for Gemini
        if image_data:
            logger.info(f"Processing image with message: {message[:100]}...")

            try:
                # Decode base64 image
                image_bytes = base64.b64decode(image_data)
                image = Image.open(io.BytesIO(image_bytes))

                # Convert to RGB if necessary
                if image.mode != 'RGB':
                    image = image.convert('RGB')

                logger.info(f"Image processed: {image.size}, mode: {image.mode}")

                # Create content with both text and image for Gemini Vision
                if message:
                    content = [message, image]
                else:
                    content = ["Analyze this image and solve any mathematical problems shown. Use LaTeX formatting for math.", image]

                logger.info(f"Content prepared for Gemini: text + image")

            except Exception as img_error:
                logger.error(f"Image processing error: {img_error}")
                return jsonify({
                    'error': f'Failed to process image: {str(img_error)}',
                    'status': 'error'
                }), 400
        else:
            # Text-only content
            content = message
            logger.info(f"Processing text-only message: {message[:100]}...")

        # Generate response using Gemini
        logger.info("🤖 Sending content to Gemini...")
        response = model.generate_content(content)
        logger.info("✅ Gemini response received")

        logger.info("AI response generated successfully")

        return jsonify({
            'response': response.text,
            'status': 'success'
        })

    except Exception as e:
        logger.error(f"AI chat error: {e}")
        return jsonify({
            'error': f'Failed to generate response: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/health')
def health_check():
    return jsonify({
        "status": "healthy",
        "websocket_endpoint": "/ws",
        "ai_enabled": True,
        "port": PORT
    })

if __name__ == '__main__':
    import sys

    # Check if running with local network access
    local_network = '--local-network' in sys.argv or '--network' in sys.argv

    if local_network:
        logger.info(f"Starting unified Flask app with LOCAL NETWORK ACCESS on port {PORT}")
        logger.info("This will be accessible from other devices on your local network")

        # Get local IP address
        import socket
        try:
            # Connect to a remote address to determine local IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            logger.info(f"Your local IP address: {local_ip}")
            logger.info(f"Other devices can access at: http://{local_ip}:{PORT}")
        except Exception as e:
            logger.warning(f"Could not determine local IP: {e}")
            logger.info(f"Other devices can access at: http://[YOUR_IP]:{PORT}")
    else:
        logger.info(f"Starting unified Flask app with WebSocket on port {PORT}")
        logger.info("This will only be accessible from localhost (this device)")

    logger.info(f"AI features enabled with API key: {API_KEY[:10]}...")
    logger.info("WebSocket endpoint: /ws")

    # Start Flask app with WebSocket support
    host = '0.0.0.0' if local_network else '127.0.0.1'
    app.run(host=host, port=PORT, debug=False)
