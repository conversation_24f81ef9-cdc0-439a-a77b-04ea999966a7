#!/bin/bash

# Collaborative Whiteboard - ngrok Global Access Script for macOS/Linux
# This script starts the whiteboard with global internet access via ngrok

echo "🌍 Starting Collaborative Whiteboard with ngrok (Global Access)"
echo ""

# Check if we're in the right directory
if [ ! -f "ngrok_app.py" ]; then
    echo "❌ ERROR: ngrok_app.py not found!"
    echo "Please make sure you're in the correct directory."
    read -p "Press Enter to exit..."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  WARNING: .env file not found!"
    echo "Creating a template .env file..."
    echo "GEMINI_API_KEY=your_api_key_here" > .env
    echo "Please edit .env file and add your actual API key."
    read -p "Press Enter to continue..."
fi

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ ERROR: Python 3 not found!"
    echo "Please install Python 3 from https://python.org"
    read -p "Press Enter to exit..."
    exit 1
fi

# Check if ngrok is installed
if ! command -v ngrok &> /dev/null; then
    echo "❌ ERROR: ngrok not found!"
    echo ""
    echo "Please install ngrok:"
    echo "1. Go to https://ngrok.com/download"
    echo "2. Download ngrok for macOS"
    echo "3. Install it using one of these methods:"
    echo ""
    echo "   Method 1 - Homebrew (recommended):"
    echo "   brew install ngrok/ngrok/ngrok"
    echo ""
    echo "   Method 2 - Manual installation:"
    echo "   - Unzip the downloaded file"
    echo "   - Move ngrok to /usr/local/bin/"
    echo "   - Or place it in this project folder"
    echo ""
    read -p "Press Enter to exit..."
    exit 1
fi

# Check if required packages are installed
echo "📦 Checking required packages..."
python3 -c "import flask, flask_socketio, websockets, requests, dotenv" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "📦 Installing required packages..."
    pip3 install flask flask-socketio websockets requests python-dotenv Pillow
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install packages. Please run manually:"
        echo "pip3 install flask flask-socketio websockets requests python-dotenv Pillow"
        read -p "Press Enter to exit..."
        exit 1
    fi
fi

echo ""
echo "========================================"
echo "STARTING GLOBAL ACCESS SETUP..."
echo "========================================"
echo ""

echo "Step 1: Starting Flask + WebSocket server..."

# Start the Flask server in background
python3 ngrok_app.py &
SERVER_PID=$!

# Wait for server to start
echo "Waiting for server to start..."
sleep 3

echo ""
echo "Step 2: Starting ngrok tunnel..."
echo ""
echo "🌍 ngrok will create a public URL for global access"
echo "📋 Copy the HTTPS URL from ngrok and share it!"
echo ""

# Start ngrok (this will run in foreground)
ngrok http 5002

# When ngrok exits, kill the server
echo ""
echo "🛑 Stopping Flask server..."
kill $SERVER_PID 2>/dev/null

echo ""
echo "========================================"
echo "GLOBAL SETUP STOPPED"
echo "========================================"
echo ""
echo "Both the server and ngrok tunnel have been stopped."
echo "The whiteboard is no longer accessible globally."
echo ""
read -p "Press Enter to exit..."
