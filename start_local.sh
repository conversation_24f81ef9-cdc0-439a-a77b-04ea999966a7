#!/bin/bash

# Collaborative Whiteboard - Local Startup Script for macOS/Linux
# This script starts the whiteboard for localhost access only

echo "🎨 Starting Collaborative Whiteboard Locally (Localhost Only)"
echo ""

# Check if we're in the right directory
if [ ! -f "ngrok_app.py" ]; then
    echo "❌ ERROR: ngrok_app.py not found!"
    echo "Please make sure you're in the correct directory."
    read -p "Press Enter to exit..."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  WARNING: .env file not found!"
    echo "Creating a template .env file..."
    echo "GEMINI_API_KEY=your_api_key_here" > .env
    echo "Please edit .env file and add your actual API key."
    read -p "Press Enter to continue..."
fi

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ ERROR: Python 3 not found!"
    echo "Please install Python 3 from https://python.org"
    read -p "Press Enter to exit..."
    exit 1
fi

# Check if required packages are installed
echo "📦 Checking required packages..."
python3 -c "import flask, flask_socketio, websockets, requests, dotenv" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "📦 Installing required packages..."
    pip3 install flask flask-socketio websockets requests python-dotenv Pillow
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install packages. Please run manually:"
        echo "pip3 install flask flask-socketio websockets requests python-dotenv Pillow"
        read -p "Press Enter to exit..."
        exit 1
    fi
fi

echo ""
echo "🚀 Starting unified Flask + WebSocket server..."
echo ""
echo "========================================"
echo "LOCAL SETUP STARTING..."
echo "========================================"
echo ""

# Start the server
python3 ngrok_app.py

echo ""
echo "========================================"
echo "Your whiteboard was running at:"
echo "http://localhost:5002"
echo "========================================"
echo ""
echo "This works on localhost only (this device)."
echo "To allow other devices to connect, use ./start_local_network.sh"
echo "To make it globally accessible, use ./start_ngrok.sh"
echo ""
read -p "Press Enter to exit..."
