# 🍎 Collaborative Whiteboard Setup Guide for macOS

## 📋 Prerequisites for Mac

1. **macOS 10.14+** (Mojave or later)
2. **Python 3.7+** (usually pre-installed on modern Macs)
3. **Terminal** (built-in)
4. **Internet connection** for package installation
5. **Gemini API Key** (for AI features)

## 🚀 Quick Setup for Mac

### 1. Open Terminal
- Press `Cmd + Space` and type "Terminal"
- Or go to Applications → Utilities → Terminal

### 2. Navigate to Project Folder
```bash
cd /path/to/your/project/folder
# Example: cd ~/Desktop/collaborative-whiteboard
```

### 3. Make Scripts Executable (One-time setup)
```bash
chmod +x *.sh
```

### 4. Install Python Dependencies
```bash
pip3 install flask flask-socketio websockets requests python-dotenv Pillow
```

### 5. Create API Key File
```bash
echo "GEMINI_API_KEY=your_actual_api_key_here" > .env
```

## 🖥️ Running Options for Mac

### Option A: Localhost Only
**Use this for:** Testing on your Mac only

```bash
./start_local.sh
```

**Access:** http://localhost:5002

---

### Option B: Local Network Access
**Use this for:** Multiple devices on same WiFi (iPhone, iPad, other Macs)

```bash
./start_local_network.sh
```

**On your Mac:** Note the IP address shown (e.g., `*************`)
**On other devices:** Go to `http://*************:5002`

---

### Option C: Global Access (ngrok)
**Use this for:** Access from anywhere in the world

#### First, install ngrok:

**Method 1 - Homebrew (Recommended):**
```bash
# Install Homebrew if you don't have it
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install ngrok
brew install ngrok/ngrok/ngrok
```

**Method 2 - Manual Download:**
1. Go to https://ngrok.com/download
2. Download the macOS version
3. Unzip and move to `/usr/local/bin/`

#### Then run:
```bash
./start_ngrok.sh
```

Copy the ngrok HTTPS URL and share it globally!

---

## 🔧 Mac-Specific Troubleshooting

### "Permission denied" when running scripts
```bash
chmod +x *.sh
```

### "python3: command not found"
Install Python 3:
```bash
# Using Homebrew
brew install python3

# Or download from python.org
```

### "pip3: command not found"
```bash
# Install pip3
curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py
python3 get-pip.py
```

### Firewall blocking connections
1. Go to System Preferences → Security & Privacy → Firewall
2. Click "Firewall Options"
3. Allow incoming connections for Python

### Port 5002 already in use
```bash
# Find what's using the port
lsof -i :5002

# Kill the process (replace PID with actual number)
kill -9 PID
```

---

## 📱 Cross-Platform Usage

### From Mac to Other Devices:

**iPhone/iPad:**
1. Connect to same WiFi as Mac
2. Open Safari
3. Go to `http://[MAC_IP]:5002`

**Windows PC:**
1. Connect to same WiFi as Mac
2. Open any browser
3. Go to `http://[MAC_IP]:5002`

**Android:**
1. Connect to same WiFi as Mac
2. Open Chrome
3. Go to `http://[MAC_IP]:5002`

---

## 🎯 Demo Setup for Teacher

### Recommended Setup:
1. **Use your MacBook** as the server
2. **Run:** `./start_local_network.sh`
3. **Note the IP address** (e.g., *************)
4. **Share with class:** "Go to http://*************:5002"
5. **Everyone collaborates** in real-time!

### For Global Demo:
1. **Run:** `./start_ngrok.sh`
2. **Copy the ngrok URL** (e.g., https://abc123.ngrok-free.app)
3. **Share globally** - works from anywhere!

---

## 🔄 Stopping the Server

### To stop localhost/network server:
- Press `Ctrl + C` in Terminal

### To stop ngrok:
- Press `Ctrl + C` in Terminal (stops both ngrok and server)

---

## 🆘 Quick Commands Reference

```bash
# Make scripts executable
chmod +x *.sh

# Install dependencies
pip3 install flask flask-socketio websockets requests python-dotenv Pillow

# Run localhost only
./start_local.sh

# Run with network access
./start_local_network.sh

# Run with global access
./start_ngrok.sh

# Check if server is running
lsof -i :5002

# Stop server if stuck
pkill -f "python3 ngrok_app.py"
```

---

## 🎉 Features Available on Mac

✅ **Real-time Collaboration** - Multiple users drawing simultaneously  
✅ **Cross-Platform** - Works with iPhone, iPad, Windows, Android  
✅ **AI Assistant** - Analyze drawings and solve math problems  
✅ **Video Conferencing** - Google Meet style video calls  
✅ **Canvas Synchronization** - Late joiners see existing content  
✅ **Global Access** - Share with anyone worldwide via ngrok  

Perfect for classroom demonstrations and collaborative learning! 🚀
