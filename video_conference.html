<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Conference - Collaborative Whiteboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .video-container {
            position: relative;
            background: #1f2937;
            border-radius: 12px;
            overflow: hidden;
            aspect-ratio: 16/9;
        }
        
        .video-container video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .participant-overlay {
            position: absolute;
            bottom: 8px;
            left: 8px;
            right: 8px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .main-video {
            aspect-ratio: 16/9;
            max-height: 70vh;
        }
        
        .participant-grid {
            display: grid;
            gap: 12px;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            max-height: 200px;
            overflow-y: auto;
        }
        
        .controls-panel {
            background: rgba(31, 41, 55, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 16px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }
        
        .control-btn {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            cursor: pointer;
        }
        
        .control-btn:hover {
            transform: scale(1.1);
        }
        
        .control-btn.active {
            background: #ef4444;
        }
        
        .control-btn.inactive {
            background: #6b7280;
        }
        
        .end-call-btn {
            background: #dc2626;
        }
        
        .end-call-btn:hover {
            background: #b91c1c;
        }
        
        .connection-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }
        
        .status-excellent { background: #10b981; }
        .status-good { background: #f59e0b; }
        .status-poor { background: #ef4444; }
        .status-disconnected { background: #6b7280; }
        
        .participant-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .screen-share-container {
            position: relative;
            background: #000;
            border-radius: 12px;
            overflow: hidden;
        }
        
        .floating-controls {
            position: fixed;
            bottom: 24px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 min-h-screen text-white">
    <!-- Header -->
    <header class="bg-black bg-opacity-30 backdrop-filter backdrop-blur-lg border-b border-gray-700">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <h1 class="text-2xl font-bold">Video Conference</h1>
                    <div class="flex items-center space-x-2 text-sm text-gray-300">
                        <i class="fas fa-users"></i>
                        <span id="room-info">Room: Loading...</span>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                    <!-- Connection Status -->
                    <div class="connection-indicator">
                        <div id="connection-status-dot" class="status-dot status-disconnected"></div>
                        <span id="connection-status-text">Connecting...</span>
                    </div>
                    
                    <!-- Call Duration -->
                    <div class="flex items-center space-x-2 text-sm">
                        <i class="fas fa-clock"></i>
                        <span id="call-duration">00:00</span>
                    </div>
                    
                    <!-- Participant Count -->
                    <div class="flex items-center space-x-2 text-sm">
                        <i class="fas fa-users"></i>
                        <span id="participant-count">0</span>
                    </div>
                    
                    <!-- Settings -->
                    <button id="settings-btn" class="p-2 hover:bg-gray-700 rounded-lg transition-colors">
                        <i class="fas fa-cog"></i>
                    </button>
                    
                    <!-- Close Conference -->
                    <button id="close-conference-btn" class="p-2 hover:bg-red-600 rounded-lg transition-colors text-red-400">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-6 flex-1">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 h-full">
            <!-- Video Area -->
            <div class="lg:col-span-3 space-y-6">
                <!-- Main Video/Screen Share -->
                <div id="main-video-container" class="video-container main-video">
                    <video id="main-video" autoplay muted playsinline class="hidden"></video>
                    <div id="screen-share-container" class="screen-share-container hidden">
                        <video id="screen-share-video" autoplay playsinline></video>
                        <div class="participant-overlay">
                            <span id="screen-share-user">Screen Share</span>
                            <button id="stop-screen-share" class="text-red-400 hover:text-red-300">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div id="no-video-placeholder" class="flex items-center justify-center h-full">
                        <div class="text-center">
                            <i class="fas fa-video-slash text-6xl text-gray-500 mb-4"></i>
                            <p class="text-gray-400">No video stream</p>
                        </div>
                    </div>
                </div>
                
                <!-- Participant Grid -->
                <div id="participants-container" class="participant-grid">
                    <!-- Participant videos will be added here dynamically -->
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="lg:col-span-1 space-y-6">
                <!-- Participants Panel -->
                <div class="bg-gray-800 bg-opacity-50 backdrop-filter backdrop-blur-lg rounded-xl p-4">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <i class="fas fa-users mr-2"></i>
                        Participants
                    </h3>
                    <div id="participants-list" class="participant-list space-y-2">
                        <!-- Participants will be listed here -->
                    </div>
                </div>
                
                <!-- Chat Panel -->
                <div class="bg-gray-800 bg-opacity-50 backdrop-filter backdrop-blur-lg rounded-xl p-4 flex-1">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <i class="fas fa-comments mr-2"></i>
                        Chat
                    </h3>
                    <div id="chat-messages" class="space-y-2 mb-4 max-h-40 overflow-y-auto">
                        <!-- Chat messages will appear here -->
                    </div>
                    <div class="flex space-x-2">
                        <input id="chat-input" type="text" placeholder="Type a message..." 
                               class="flex-1 bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-blue-500">
                        <button id="send-chat" class="bg-blue-600 hover:bg-blue-700 px-3 py-2 rounded-lg transition-colors">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Floating Controls -->
    <div class="floating-controls">
        <div class="controls-panel">
            <div class="flex items-center space-x-4">
                <!-- Microphone -->
                <button id="mic-btn" class="control-btn active" title="Toggle Microphone">
                    <i class="fas fa-microphone"></i>
                </button>
                
                <!-- Camera -->
                <button id="camera-btn" class="control-btn active" title="Toggle Camera">
                    <i class="fas fa-video"></i>
                </button>
                
                <!-- Screen Share -->
                <button id="screen-share-btn" class="control-btn inactive" title="Share Screen">
                    <i class="fas fa-desktop"></i>
                </button>
                
                <!-- Settings -->
                <button id="device-settings-btn" class="control-btn inactive" title="Device Settings">
                    <i class="fas fa-cog"></i>
                </button>
                
                <!-- End Call -->
                <button id="end-call-btn" class="control-btn end-call-btn" title="End Call">
                    <i class="fas fa-phone-slash"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div id="settings-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-gray-800 rounded-xl p-6 max-w-md w-full mx-4">
            <h3 class="text-xl font-semibold mb-4">Device Settings</h3>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium mb-2">Camera</label>
                    <select id="camera-select" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2">
                        <option>Loading cameras...</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">Microphone</label>
                    <select id="microphone-select" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2">
                        <option>Loading microphones...</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">Speaker</label>
                    <select id="speaker-select" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2">
                        <option>Loading speakers...</option>
                    </select>
                </div>
            </div>
            
            <div class="flex justify-end space-x-3 mt-6">
                <button id="cancel-settings" class="px-4 py-2 text-gray-400 hover:text-white transition-colors">
                    Cancel
                </button>
                <button id="apply-settings" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors">
                    Apply
                </button>
            </div>
        </div>
    </div>

    <script>
        // Global variables for WebRTC and application state
        let localStream = null;
        let screenStream = null;
        let peerConnections = new Map();
        let connectionStates = new Map();
        let participants = new Map();
        let isCallActive = false;
        let isMicMuted = false;
        let isCameraOff = false;
        let isScreenSharing = false;
        let callStartTime = null;
        let callDurationInterval = null;
        let collaborationSocket = null;
        let currentUserId = null;
        let currentRoomId = null;
        let currentUserName = null;

        // WebRTC Configuration
        const rtcConfiguration = {
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' },
                { urls: 'stun:stun2.l.google.com:19302' },
                { urls: 'stun:stun3.l.google.com:19302' }
            ],
            iceCandidatePoolSize: 10
        };

        // Initialize the video conference application
        document.addEventListener('DOMContentLoaded', initializeVideoConference);

        function initializeVideoConference() {
            console.log('Initializing Video Conference...');

            // Get room and user info from URL parameters or localStorage
            const urlParams = new URLSearchParams(window.location.search);
            currentRoomId = urlParams.get('room') || localStorage.getItem('currentRoomId');
            currentUserName = urlParams.get('user') || localStorage.getItem('userName') || 'Anonymous';

            if (!currentRoomId) {
                alert('No room specified. Please join a room first.');
                window.close();
                return;
            }

            // Update UI with room info
            document.getElementById('room-info').textContent = `Room: ${currentRoomId}`;

            // Initialize WebSocket connection
            initializeWebSocket();

            // Setup event listeners
            setupEventListeners();

            // Initialize media devices
            initializeMediaDevices();
        }

        // WebSocket Connection Management
        function initializeWebSocket() {
            const wsUrl = 'ws://localhost:8765';
            collaborationSocket = new WebSocket(wsUrl);

            collaborationSocket.onopen = () => {
                console.log('WebSocket connected');
                updateConnectionStatus('connected', 'Connected');

                // Join the room
                collaborationSocket.send(JSON.stringify({
                    type: 'join_room',
                    room_id: currentRoomId,
                    name: currentUserName
                }));
            };

            collaborationSocket.onmessage = handleWebSocketMessage;

            collaborationSocket.onclose = () => {
                console.log('WebSocket disconnected');
                updateConnectionStatus('disconnected', 'Disconnected');

                // Attempt to reconnect after 3 seconds
                setTimeout(() => {
                    if (!collaborationSocket || collaborationSocket.readyState === WebSocket.CLOSED) {
                        initializeWebSocket();
                    }
                }, 3000);
            };

            collaborationSocket.onerror = (error) => {
                console.error('WebSocket error:', error);
                updateConnectionStatus('error', 'Connection Error');
            };
        }

        function handleWebSocketMessage(event) {
            const data = JSON.parse(event.data);
            console.log('Received message:', data);

            switch (data.type) {
                case 'room_joined':
                    currentUserId = data.user_id;
                    updateParticipantsList(data.users);
                    startCall();
                    break;

                case 'user_joined':
                    addParticipant(data.user_id, data.name);
                    if (isCallActive) {
                        // Send offer to new user
                        setTimeout(() => sendWebRTCOffer(data.user_id), 1000);
                    }
                    break;

                case 'user_left':
                    removeParticipant(data.user_id);
                    break;

                case 'webrtc_offer':
                    handleWebRTCOffer(data.from_user_id, data.offer);
                    break;

                case 'webrtc_answer':
                    handleWebRTCAnswer(data.from_user_id, data.answer);
                    break;

                case 'webrtc_ice_candidate':
                    handleWebRTCIceCandidate(data.from_user_id, data.candidate);
                    break;

                case 'chat_message':
                    addChatMessage(data.user_name, data.message);
                    break;
            }
        }

        function updateConnectionStatus(status, text) {
            const statusDot = document.getElementById('connection-status-dot');
            const statusText = document.getElementById('connection-status-text');

            statusDot.className = `status-dot status-${status}`;
            statusText.textContent = text;
        }

        // Media Device Management
        async function initializeMediaDevices() {
            try {
                // Get user media
                localStream = await navigator.mediaDevices.getUserMedia({
                    video: { width: 1280, height: 720, frameRate: 30 },
                    audio: { echoCancellation: true, noiseSuppression: true }
                });

                // Display local video
                const mainVideo = document.getElementById('main-video');
                mainVideo.srcObject = localStream;
                mainVideo.classList.remove('hidden');
                document.getElementById('no-video-placeholder').classList.add('hidden');

                // Populate device selectors
                await populateDeviceSelectors();

                console.log('Media devices initialized successfully');
            } catch (error) {
                console.error('Error accessing media devices:', error);
                showError('Failed to access camera/microphone. Please check permissions.');
            }
        }

        async function populateDeviceSelectors() {
            try {
                const devices = await navigator.mediaDevices.enumerateDevices();

                const cameraSelect = document.getElementById('camera-select');
                const micSelect = document.getElementById('microphone-select');
                const speakerSelect = document.getElementById('speaker-select');

                // Clear existing options
                cameraSelect.innerHTML = '';
                micSelect.innerHTML = '';
                speakerSelect.innerHTML = '';

                devices.forEach(device => {
                    const option = document.createElement('option');
                    option.value = device.deviceId;
                    option.textContent = device.label || `${device.kind} ${device.deviceId.slice(0, 8)}`;

                    if (device.kind === 'videoinput') {
                        cameraSelect.appendChild(option);
                    } else if (device.kind === 'audioinput') {
                        micSelect.appendChild(option);
                    } else if (device.kind === 'audiooutput') {
                        speakerSelect.appendChild(option);
                    }
                });
            } catch (error) {
                console.error('Error enumerating devices:', error);
            }
        }

        // WebRTC Peer Connection Management
        function createPeerConnection(userId) {
            const peerConnection = new RTCPeerConnection(rtcConfiguration);

            // Add local stream tracks
            if (localStream) {
                localStream.getTracks().forEach(track => {
                    peerConnection.addTrack(track, localStream);
                });
            }

            // Handle remote stream
            peerConnection.ontrack = (event) => {
                console.log('Received remote track from:', userId);
                const remoteStream = event.streams[0];
                addParticipantVideo(userId, remoteStream);
            };

            // Handle ICE candidates
            peerConnection.onicecandidate = (event) => {
                if (event.candidate) {
                    sendWebRTCMessage('webrtc_ice_candidate', {
                        target_user_id: userId,
                        candidate: event.candidate
                    });
                }
            };

            // Handle connection state changes
            peerConnection.onconnectionstatechange = () => {
                console.log(`Connection state with ${userId}:`, peerConnection.connectionState);
                connectionStates.set(userId, peerConnection.connectionState);
                updateParticipantConnectionStatus(userId, peerConnection.connectionState);

                if (peerConnection.connectionState === 'failed') {
                    console.log('Connection failed, attempting to reconnect...');
                    setTimeout(() => reconnectToPeer(userId), 2000);
                }
            };

            // Handle ICE connection state changes
            peerConnection.oniceconnectionstatechange = () => {
                console.log(`ICE connection state with ${userId}:`, peerConnection.iceConnectionState);
            };

            peerConnections.set(userId, peerConnection);
            return peerConnection;
        }

        async function sendWebRTCOffer(userId) {
            try {
                const peerConnection = createPeerConnection(userId);
                const offer = await peerConnection.createOffer();
                await peerConnection.setLocalDescription(offer);

                sendWebRTCMessage('webrtc_offer', {
                    target_user_id: userId,
                    offer: offer
                });

                console.log('WebRTC offer sent to:', userId);
            } catch (error) {
                console.error('Error creating/sending offer:', error);
                showError(`Failed to connect to participant: ${error.message}`);
            }
        }

        async function handleWebRTCOffer(fromUserId, offer) {
            try {
                const peerConnection = createPeerConnection(fromUserId);
                await peerConnection.setRemoteDescription(offer);

                const answer = await peerConnection.createAnswer();
                await peerConnection.setLocalDescription(answer);

                sendWebRTCMessage('webrtc_answer', {
                    target_user_id: fromUserId,
                    answer: answer
                });

                console.log('WebRTC answer sent to:', fromUserId);
            } catch (error) {
                console.error('Error handling offer:', error);
                showError(`Failed to respond to connection: ${error.message}`);
            }
        }

        async function handleWebRTCAnswer(fromUserId, answer) {
            try {
                const peerConnection = peerConnections.get(fromUserId);
                if (peerConnection) {
                    await peerConnection.setRemoteDescription(answer);
                    console.log('WebRTC answer processed for:', fromUserId);
                }
            } catch (error) {
                console.error('Error handling answer:', error);
            }
        }

        async function handleWebRTCIceCandidate(fromUserId, candidate) {
            try {
                const peerConnection = peerConnections.get(fromUserId);
                if (peerConnection) {
                    await peerConnection.addIceCandidate(candidate);
                    console.log('ICE candidate added for:', fromUserId);
                }
            } catch (error) {
                console.error('Error adding ICE candidate:', error);
            }
        }

        function sendWebRTCMessage(type, data) {
            if (collaborationSocket && collaborationSocket.readyState === WebSocket.OPEN) {
                collaborationSocket.send(JSON.stringify({
                    type: type,
                    ...data
                }));
            }
        }

        async function reconnectToPeer(userId) {
            console.log('Attempting to reconnect to:', userId);

            // Close existing connection
            const existingConnection = peerConnections.get(userId);
            if (existingConnection) {
                existingConnection.close();
                peerConnections.delete(userId);
            }

            // Create new connection and send offer
            await sendWebRTCOffer(userId);
        }

        // Participant Management
        function updateParticipantsList(users) {
            participants.clear();
            users.forEach(user => {
                if (user.user_id !== currentUserId) {
                    participants.set(user.user_id, {
                        name: user.name,
                        connectionState: 'connecting'
                    });
                }
            });
            renderParticipantsList();
            updateParticipantCount();
        }

        function addParticipant(userId, name) {
            participants.set(userId, {
                name: name,
                connectionState: 'connecting'
            });
            renderParticipantsList();
            updateParticipantCount();
        }

        function removeParticipant(userId) {
            participants.delete(userId);

            // Close peer connection
            const peerConnection = peerConnections.get(userId);
            if (peerConnection) {
                peerConnection.close();
                peerConnections.delete(userId);
            }

            // Remove video element
            const videoElement = document.getElementById(`participant-${userId}`);
            if (videoElement) {
                videoElement.remove();
            }

            renderParticipantsList();
            updateParticipantCount();
        }

        function renderParticipantsList() {
            const participantsList = document.getElementById('participants-list');
            participantsList.innerHTML = '';

            // Add self
            const selfItem = createParticipantListItem(currentUserId, `${currentUserName} (You)`, 'connected');
            participantsList.appendChild(selfItem);

            // Add other participants
            participants.forEach((participant, userId) => {
                const item = createParticipantListItem(userId, participant.name, participant.connectionState);
                participantsList.appendChild(item);
            });
        }

        function createParticipantListItem(userId, name, connectionState) {
            const item = document.createElement('div');
            item.className = 'flex items-center justify-between p-2 bg-gray-700 rounded-lg';

            const statusClass = {
                'connected': 'status-excellent',
                'connecting': 'status-good',
                'disconnected': 'status-poor',
                'failed': 'status-poor'
            }[connectionState] || 'status-disconnected';

            item.innerHTML = `
                <div class="flex items-center space-x-3">
                    <div class="status-dot ${statusClass}"></div>
                    <span class="text-sm">${name}</span>
                </div>
                <div class="flex space-x-1">
                    ${userId !== currentUserId ? `
                        <button class="text-xs text-gray-400 hover:text-white" onclick="muteParticipant('${userId}')">
                            <i class="fas fa-microphone-slash"></i>
                        </button>
                    ` : ''}
                </div>
            `;

            return item;
        }

        function updateParticipantConnectionStatus(userId, connectionState) {
            if (participants.has(userId)) {
                participants.get(userId).connectionState = connectionState;
                renderParticipantsList();
            }
        }

        function updateParticipantCount() {
            const count = participants.size + 1; // +1 for self
            document.getElementById('participant-count').textContent = count;
        }

        function addParticipantVideo(userId, stream) {
            const participantsContainer = document.getElementById('participants-container');

            // Remove existing video if any
            const existingVideo = document.getElementById(`participant-${userId}`);
            if (existingVideo) {
                existingVideo.remove();
            }

            // Create video container
            const videoContainer = document.createElement('div');
            videoContainer.id = `participant-${userId}`;
            videoContainer.className = 'video-container';

            // Create video element
            const video = document.createElement('video');
            video.autoplay = true;
            video.playsinline = true;
            video.srcObject = stream;

            // Create overlay
            const overlay = document.createElement('div');
            overlay.className = 'participant-overlay';

            const participantName = participants.get(userId)?.name || 'Unknown';
            overlay.innerHTML = `
                <span>${participantName}</span>
                <div class="flex items-center space-x-2">
                    <div class="status-dot status-excellent"></div>
                </div>
            `;

            videoContainer.appendChild(video);
            videoContainer.appendChild(overlay);
            participantsContainer.appendChild(videoContainer);
        }

        // Call Management
        function startCall() {
            isCallActive = true;
            callStartTime = Date.now();
            startCallDurationTimer();

            // Send offers to all participants
            participants.forEach((participant, userId) => {
                setTimeout(() => sendWebRTCOffer(userId), Math.random() * 1000);
            });

            // Notify main application about video conference start
            notifyMainApplication('video_conference_started', {
                room_id: currentRoomId,
                user_name: currentUserName
            });

            console.log('Call started');
        }

        function endCall() {
            isCallActive = false;

            // Stop call duration timer
            if (callDurationInterval) {
                clearInterval(callDurationInterval);
                callDurationInterval = null;
            }

            // Close all peer connections
            peerConnections.forEach(pc => pc.close());
            peerConnections.clear();
            connectionStates.clear();

            // Stop local media
            if (localStream) {
                localStream.getTracks().forEach(track => track.stop());
                localStream = null;
            }

            if (screenStream) {
                screenStream.getTracks().forEach(track => track.stop());
                screenStream = null;
            }

            // Notify main application about video conference end
            notifyMainApplication('video_conference_ended', {
                room_id: currentRoomId,
                user_name: currentUserName
            });

            // Close the conference window
            window.close();
        }

        function startCallDurationTimer() {
            callDurationInterval = setInterval(() => {
                if (callStartTime) {
                    const duration = Date.now() - callStartTime;
                    const minutes = Math.floor(duration / 60000);
                    const seconds = Math.floor((duration % 60000) / 1000);
                    document.getElementById('call-duration').textContent =
                        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                }
            }, 1000);
        }

        // Media Controls
        function toggleMicrophone() {
            if (localStream) {
                const audioTrack = localStream.getAudioTracks()[0];
                if (audioTrack) {
                    audioTrack.enabled = !audioTrack.enabled;
                    isMicMuted = !audioTrack.enabled;

                    const micBtn = document.getElementById('mic-btn');
                    const icon = micBtn.querySelector('i');

                    if (isMicMuted) {
                        micBtn.classList.remove('active');
                        micBtn.classList.add('inactive');
                        icon.className = 'fas fa-microphone-slash';
                    } else {
                        micBtn.classList.remove('inactive');
                        micBtn.classList.add('active');
                        icon.className = 'fas fa-microphone';
                    }
                }
            }
        }

        function toggleCamera() {
            if (localStream) {
                const videoTrack = localStream.getVideoTracks()[0];
                if (videoTrack) {
                    videoTrack.enabled = !videoTrack.enabled;
                    isCameraOff = !videoTrack.enabled;

                    const cameraBtn = document.getElementById('camera-btn');
                    const icon = cameraBtn.querySelector('i');

                    if (isCameraOff) {
                        cameraBtn.classList.remove('active');
                        cameraBtn.classList.add('inactive');
                        icon.className = 'fas fa-video-slash';
                        document.getElementById('main-video').classList.add('hidden');
                        document.getElementById('no-video-placeholder').classList.remove('hidden');
                    } else {
                        cameraBtn.classList.remove('inactive');
                        cameraBtn.classList.add('active');
                        icon.className = 'fas fa-video';
                        document.getElementById('main-video').classList.remove('hidden');
                        document.getElementById('no-video-placeholder').classList.add('hidden');
                    }
                }
            }
        }

        // Screen Sharing
        async function toggleScreenShare() {
            if (!isScreenSharing) {
                await startScreenShare();
            } else {
                stopScreenShare();
            }
        }

        async function startScreenShare() {
            try {
                screenStream = await navigator.mediaDevices.getDisplayMedia({
                    video: { mediaSource: 'screen' },
                    audio: true
                });

                isScreenSharing = true;

                // Update UI
                const screenShareBtn = document.getElementById('screen-share-btn');
                screenShareBtn.classList.remove('inactive');
                screenShareBtn.classList.add('active');

                // Show screen share video
                const screenShareVideo = document.getElementById('screen-share-video');
                const screenShareContainer = document.getElementById('screen-share-container');
                screenShareVideo.srcObject = screenStream;
                screenShareContainer.classList.remove('hidden');
                document.getElementById('main-video').classList.add('hidden');

                // Replace video track in all peer connections
                const videoTrack = screenStream.getVideoTracks()[0];
                peerConnections.forEach(async (pc) => {
                    const sender = pc.getSenders().find(s => s.track && s.track.kind === 'video');
                    if (sender) {
                        await sender.replaceTrack(videoTrack);
                    }
                });

                // Handle screen share end
                videoTrack.onended = () => {
                    stopScreenShare();
                };

                console.log('Screen sharing started');
            } catch (error) {
                console.error('Error starting screen share:', error);
                showError('Failed to start screen sharing');
            }
        }

        function stopScreenShare() {
            if (screenStream) {
                screenStream.getTracks().forEach(track => track.stop());
                screenStream = null;
            }

            isScreenSharing = false;

            // Update UI
            const screenShareBtn = document.getElementById('screen-share-btn');
            screenShareBtn.classList.remove('active');
            screenShareBtn.classList.add('inactive');

            // Hide screen share video
            const screenShareContainer = document.getElementById('screen-share-container');
            screenShareContainer.classList.add('hidden');

            if (!isCameraOff) {
                document.getElementById('main-video').classList.remove('hidden');
            } else {
                document.getElementById('no-video-placeholder').classList.remove('hidden');
            }

            // Replace back to camera track in all peer connections
            if (localStream) {
                const videoTrack = localStream.getVideoTracks()[0];
                peerConnections.forEach(async (pc) => {
                    const sender = pc.getSenders().find(s => s.track && s.track.kind === 'video');
                    if (sender && videoTrack) {
                        await sender.replaceTrack(videoTrack);
                    }
                });
            }

            console.log('Screen sharing stopped');
        }

        // Event Listeners Setup
        function setupEventListeners() {
            // Control buttons
            document.getElementById('mic-btn').addEventListener('click', toggleMicrophone);
            document.getElementById('camera-btn').addEventListener('click', toggleCamera);
            document.getElementById('screen-share-btn').addEventListener('click', toggleScreenShare);
            document.getElementById('end-call-btn').addEventListener('click', endCall);

            // Settings
            document.getElementById('settings-btn').addEventListener('click', showSettings);
            document.getElementById('device-settings-btn').addEventListener('click', showSettings);
            document.getElementById('cancel-settings').addEventListener('click', hideSettings);
            document.getElementById('apply-settings').addEventListener('click', applySettings);

            // Close conference
            document.getElementById('close-conference-btn').addEventListener('click', endCall);

            // Chat
            document.getElementById('send-chat').addEventListener('click', sendChatMessage);
            document.getElementById('chat-input').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    sendChatMessage();
                }
            });

            // Screen share stop button
            document.getElementById('stop-screen-share').addEventListener('click', stopScreenShare);

            // Window close event
            window.addEventListener('beforeunload', () => {
                endCall();
            });
        }

        // Chat Functions
        function sendChatMessage() {
            const chatInput = document.getElementById('chat-input');
            const message = chatInput.value.trim();

            if (message && collaborationSocket && collaborationSocket.readyState === WebSocket.OPEN) {
                collaborationSocket.send(JSON.stringify({
                    type: 'chat_message',
                    message: message
                }));

                chatInput.value = '';
            }
        }

        function addChatMessage(userName, message) {
            const chatMessages = document.getElementById('chat-messages');
            const messageElement = document.createElement('div');
            messageElement.className = 'text-sm';

            const time = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            messageElement.innerHTML = `
                <div class="flex justify-between items-start">
                    <span class="font-medium text-blue-300">${userName}:</span>
                    <span class="text-xs text-gray-400">${time}</span>
                </div>
                <div class="text-gray-200 mt-1">${message}</div>
            `;

            chatMessages.appendChild(messageElement);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Settings Functions
        function showSettings() {
            document.getElementById('settings-modal').classList.remove('hidden');
            document.getElementById('settings-modal').classList.add('flex');
        }

        function hideSettings() {
            document.getElementById('settings-modal').classList.add('hidden');
            document.getElementById('settings-modal').classList.remove('flex');
        }

        async function applySettings() {
            try {
                const cameraSelect = document.getElementById('camera-select');
                const micSelect = document.getElementById('microphone-select');

                const selectedCamera = cameraSelect.value;
                const selectedMic = micSelect.value;

                // Stop current stream
                if (localStream) {
                    localStream.getTracks().forEach(track => track.stop());
                }

                // Get new stream with selected devices
                localStream = await navigator.mediaDevices.getUserMedia({
                    video: selectedCamera ? { deviceId: selectedCamera } : true,
                    audio: selectedMic ? { deviceId: selectedMic } : true
                });

                // Update local video
                document.getElementById('main-video').srcObject = localStream;

                // Update all peer connections with new tracks
                peerConnections.forEach(async (pc) => {
                    const senders = pc.getSenders();
                    const tracks = localStream.getTracks();

                    for (const track of tracks) {
                        const sender = senders.find(s => s.track && s.track.kind === track.kind);
                        if (sender) {
                            await sender.replaceTrack(track);
                        }
                    }
                });

                hideSettings();
                console.log('Device settings applied');
            } catch (error) {
                console.error('Error applying settings:', error);
                showError('Failed to apply device settings');
            }
        }

        // Utility Functions
        function showError(message) {
            // Create a simple toast notification
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-red-600 text-white px-4 py-2 rounded-lg shadow-lg z-50';
            toast.textContent = message;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 5000);
        }

        function muteParticipant(userId) {
            // This would typically send a request to mute a participant
            // For now, just show a message
            showError('Participant muting not implemented in this demo');
        }

        // Advanced Features: Connection Quality Monitoring
        let qualityMonitoringInterval = null;

        function startQualityMonitoring() {
            if (qualityMonitoringInterval) {
                clearInterval(qualityMonitoringInterval);
            }

            qualityMonitoringInterval = setInterval(async () => {
                for (const [userId, pc] of peerConnections) {
                    try {
                        const stats = await pc.getStats();
                        const quality = analyzeConnectionQuality(stats);
                        updateParticipantQuality(userId, quality);
                    } catch (error) {
                        console.error('Error getting stats for', userId, error);
                    }
                }
                updateOverallConnectionQuality();
            }, 2000);
        }

        function stopQualityMonitoring() {
            if (qualityMonitoringInterval) {
                clearInterval(qualityMonitoringInterval);
                qualityMonitoringInterval = null;
            }
        }

        function analyzeConnectionQuality(stats) {
            let quality = { score: 100, issues: [] };

            stats.forEach(report => {
                if (report.type === 'inbound-rtp' && report.mediaType === 'video') {
                    // Check packet loss
                    if (report.packetsLost > 0) {
                        const lossRate = report.packetsLost / (report.packetsReceived + report.packetsLost);
                        if (lossRate > 0.05) {
                            quality.score -= 30;
                            quality.issues.push('High packet loss');
                        } else if (lossRate > 0.02) {
                            quality.score -= 15;
                            quality.issues.push('Moderate packet loss');
                        }
                    }

                    // Check frame rate
                    if (report.framesPerSecond && report.framesPerSecond < 15) {
                        quality.score -= 20;
                        quality.issues.push('Low frame rate');
                    }
                }

                if (report.type === 'candidate-pair' && report.state === 'succeeded') {
                    // Check RTT (Round Trip Time)
                    if (report.currentRoundTripTime > 0.3) {
                        quality.score -= 25;
                        quality.issues.push('High latency');
                    } else if (report.currentRoundTripTime > 0.15) {
                        quality.score -= 10;
                        quality.issues.push('Moderate latency');
                    }
                }
            });

            return Math.max(0, quality.score);
        }

        function updateParticipantQuality(userId, qualityScore) {
            const participantElement = document.getElementById(`participant-${userId}`);
            if (participantElement) {
                const statusDot = participantElement.querySelector('.status-dot');
                if (statusDot) {
                    if (qualityScore >= 80) {
                        statusDot.className = 'status-dot status-excellent';
                    } else if (qualityScore >= 60) {
                        statusDot.className = 'status-dot status-good';
                    } else {
                        statusDot.className = 'status-dot status-poor';
                    }
                }
            }
        }

        function updateOverallConnectionQuality() {
            let totalQuality = 0;
            let connectionCount = 0;

            peerConnections.forEach((pc, userId) => {
                if (pc.connectionState === 'connected') {
                    connectionCount++;
                    // Simplified quality calculation for overall status
                    totalQuality += 80; // Assume good quality for connected peers
                }
            });

            if (connectionCount === 0) {
                updateConnectionStatus('disconnected', 'No connections');
                return;
            }

            const avgQuality = totalQuality / connectionCount;
            if (avgQuality >= 80) {
                updateConnectionStatus('excellent', 'Excellent');
            } else if (avgQuality >= 60) {
                updateConnectionStatus('good', 'Good');
            } else {
                updateConnectionStatus('poor', 'Poor');
            }
        }

        // Enhanced Error Handling
        function handleWebRTCError(userId, error, context) {
            console.error(`WebRTC error with ${userId} in ${context}:`, error);

            // Show user-friendly error message
            const participantName = participants.get(userId)?.name || 'Unknown participant';
            addChatMessage('System', `Connection issue with ${participantName}: ${error.message}`);

            // Update connection state
            connectionStates.set(userId, 'failed');
            updateParticipantConnectionStatus(userId, 'failed');

            // Attempt automatic reconnection for certain error types
            if (error.name === 'NetworkError' || error.name === 'OperationError') {
                console.log(`Attempting automatic reconnection for ${userId}...`);
                setTimeout(() => {
                    if (peerConnections.has(userId) && isCallActive) {
                        reconnectToPeer(userId);
                    }
                }, 3000);
            }
        }

        // Bandwidth Optimization
        function optimizeBandwidth() {
            const participantCount = participants.size;

            // Adjust video quality based on participant count
            let maxBitrate = 1000000; // 1 Mbps default

            if (participantCount > 6) {
                maxBitrate = 300000; // 300 Kbps for large groups
            } else if (participantCount > 3) {
                maxBitrate = 500000; // 500 Kbps for medium groups
            }

            // Apply bandwidth constraints to all peer connections
            peerConnections.forEach(async (pc) => {
                const senders = pc.getSenders();
                for (const sender of senders) {
                    if (sender.track && sender.track.kind === 'video') {
                        const params = sender.getParameters();
                        if (params.encodings && params.encodings.length > 0) {
                            params.encodings[0].maxBitrate = maxBitrate;
                            await sender.setParameters(params);
                        }
                    }
                }
            });

            console.log(`Bandwidth optimized for ${participantCount} participants: ${maxBitrate} bps`);
        }

        // Enhanced call management with quality monitoring
        function enhancedStartCall() {
            startCall();
            startQualityMonitoring();

            // Optimize bandwidth after connections are established
            setTimeout(optimizeBandwidth, 5000);
        }

        function enhancedEndCall() {
            stopQualityMonitoring();
            endCall();
        }

        // Override the original functions
        const originalStartCall = startCall;
        const originalEndCall = endCall;
        startCall = enhancedStartCall;
        endCall = enhancedEndCall;

        // Cross-Application Communication
        function notifyMainApplication(eventType, data) {
            try {
                // Try to communicate with parent window (if opened as popup)
                if (window.opener && !window.opener.closed) {
                    window.opener.postMessage({
                        type: 'video_conference_event',
                        event: eventType,
                        data: data
                    }, '*');
                }

                // Also store in localStorage for persistence
                localStorage.setItem('lastVideoConferenceEvent', JSON.stringify({
                    type: eventType,
                    data: data,
                    timestamp: Date.now()
                }));

                console.log('Notified main application:', eventType, data);
            } catch (error) {
                console.error('Error notifying main application:', error);
            }
        }

        // Listen for messages from main application
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'main_app_message') {
                console.log('Received message from main app:', event.data);

                switch (event.data.action) {
                    case 'update_room_users':
                        if (event.data.users) {
                            updateParticipantsList(event.data.users);
                        }
                        break;
                    case 'room_user_joined':
                        if (event.data.user) {
                            addParticipant(event.data.user.user_id, event.data.user.name);
                        }
                        break;
                    case 'room_user_left':
                        if (event.data.user_id) {
                            removeParticipant(event.data.user_id);
                        }
                        break;
                }
            }
        });

        // Backup communication through localStorage polling
        let lastMainAppUpdate = 0;
        setInterval(() => {
            try {
                const mainAppState = localStorage.getItem('mainAppState');
                if (mainAppState) {
                    const state = JSON.parse(mainAppState);
                    if (state.timestamp > lastMainAppUpdate) {
                        lastMainAppUpdate = state.timestamp;

                        // Update room users if available
                        if (state.roomUsers && state.currentRoomId === currentRoomId) {
                            const users = Array.from(state.roomUsers.values());
                            updateParticipantsList(users);
                        }
                    }
                }
            } catch (error) {
                console.error('Error reading main app state:', error);
            }
        }, 2000);

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializeVideoConference);
    </script>
</body>
</html>
