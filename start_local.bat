@echo off
echo Starting Collaborative Whiteboard Locally (Localhost Only)
echo.

echo Checking for required files...
if not exist "ngrok_app.py" (
    echo ERROR: ngrok_app.py not found!
    echo Please make sure you're in the correct directory.
    pause
    exit /b 1
)

if not exist ".env" (
    echo WARNING: .env file not found!
    echo Creating a template .env file...
    echo GEMINI_API_KEY=your_api_key_here > .env
    echo Please edit .env file and add your actual API key.
    pause
)

echo.
echo Starting unified Flask + WebSocket server...
echo.
echo ========================================
echo LOCAL SETUP STARTING...
echo ========================================
echo.

python ngrok_app.py

echo.
echo ========================================
echo Your whiteboard was running at:
echo http://localhost:5002
echo ========================================
echo.
echo This works on localhost only (this device).
echo To allow other devices to connect, use start_local_network.bat
echo To make it globally accessible, use start_ngrok.bat
echo.
pause
